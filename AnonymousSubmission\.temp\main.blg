This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
The style file: aaai25.bst
Database file #1: aaai25.bib
You've used 15 entries,
            2840 wiz_defined-function locations,
            670 strings with 8148 characters,
and the built_in function-call counts, 11887 in all, are:
= -- 813
> -- 975
< -- 0
+ -- 364
- -- 346
* -- 981
:= -- 2087
add.period$ -- 60
call.type$ -- 15
change.case$ -- 165
chr.to.int$ -- 16
cite$ -- 15
duplicate$ -- 699
empty$ -- 587
format.name$ -- 390
if$ -- 2151
int.to.chr$ -- 3
int.to.str$ -- 1
missing$ -- 189
newline$ -- 79
num.names$ -- 60
pop$ -- 387
preamble$ -- 1
purify$ -- 165
quote$ -- 0
skip$ -- 297
stack$ -- 0
substring$ -- 264
swap$ -- 386
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 135
warning$ -- 0
while$ -- 68
width$ -- 0
write$ -- 188
